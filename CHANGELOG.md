# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added

- Enhanced demo upload functionality with improved file handling
- Drag and drop support for demo file uploads
- Click-to-browse functionality for file selection
- Improved form validation for demo uploads
- Better error handling and user feedback for uploads
- Enhanced Steam authentication flow
- Improved UI components with consistent styling
- Better mobile responsiveness with updated hooks
- **Backend**: Service layer architecture with dependency injection pattern
- **Documentation**: Git commit guidelines and development best practices
- **Infrastructure**: Root `pyproject.toml` with UV workspace configuration
- **Monorepo**: Organized package structure under `packages/` directory
- **Versioning**: Unified semver versioning system for all packages
- **Release Management**: Automated changelog updates and GitHub releases
- **CI/CD**: GitHub Actions workflow for automated releases with Docker publishing

### Changed

- **Backend**: Completed async migration for all database operations
- **Backend**: Migrated DemoService and MatchService to async patterns using python-databases package
- **Backend**: All API endpoints now use async database operations for improved performance
- **Backend**: Removed legacy synchronous database code and CRUD operations
- **Backend**: Updated service layer to use async/await patterns consistently
- **Backend**: Fixed Pydantic schema imports for better type checking compatibility
- **Backend**: Refactored demo upload API endpoint parameters for better usability
- **Backend**: Implemented service layer pattern for demo, match, and user operations
- **Backend**: Extracted business logic from main.py into organized service modules
- **Frontend**: Improved demo upload form with better UX and validation
- **Frontend**: Standardized quote style across all UI components (double to single quotes)
- **Authentication**: Streamlined Steam OpenID authentication process
- **UI/UX**: Updated styling and component layouts for better consistency
- **Code Quality**: Applied consistent code formatting across all files
- **Testing**: Enhanced test coverage and reliability for demo upload flows
- **Configuration**: Updated TypeScript and build configurations
- **Infrastructure**: Restructured project with UV workspace and packages directory
- **Monorepo**: Moved all Python packages to `packages/` directory for better organization
- **Docker**: Updated all Docker configurations to use new package paths
- **Scripts**: Updated package.json scripts to reference new backend location
- **Documentation**: Updated CLAUDE.md with new project structure information

### Fixed

- Demo file upload form validation issues
- Steam authentication callback handling
- Mobile UI responsiveness issues
- Test reliability and flakiness
- Code formatting inconsistencies
- **Frontend**: Removed unused elements and TODO comments from routes
- **Frontend**: Cleaned up dead code and improved page structure

### Technical Improvements

- Migrated from shell scripts to native pnpm and Python CLI tools
- Enhanced Docker configuration with proper health checks
- Improved development workflow with unified task runners
- Updated all README files with current architecture
- Comprehensive monorepo setup with pnpm workspaces
- Python CLI tool with Typer for backend operations
- Node.js task runner for frontend operations
- Universal task runner for cross-platform compatibility
- Multi-stage Docker builds for development and production
- Nginx reverse proxy configuration with SSL support
- GitHub Actions CI/CD pipeline
- Health check endpoints for all services
- Comprehensive documentation and guides
- **Monorepo Structure**: Implemented UV workspace configuration at root level
- **Package Organization**: Centralized all Python packages under unified `packages/` structure
- **Dependency Management**: Simplified workspace configuration with root-level UV workspace
- **Development Environment**: Updated all tooling and scripts for new package structure
- **Version Synchronization**: All packages now maintain consistent version numbers

### Removed

- Shell scripts in favor of native tooling
- Manual dependency management in favor of package managers

## [0.1.0] - 2024-01-XX

### Added

- Initial project setup
- SvelteKit frontend with TypeScript
- FastAPI backend with Python
- FastStream demo processing microservice
- Basic Docker configuration
- Redis integration for message queuing
- CS2 demo parsing capabilities
- Basic UI components with Shadcn/ui

### Infrastructure

- UV workspace for Python monorepo
- pnpm workspace for frontend
- Docker Compose for development
- Basic CI/CD pipeline

### Documentation

- Project README
- Basic setup instructions
- API documentation via FastAPI

______________________________________________________________________

## Release Notes Format

### Types of Changes

- **Added** for new features
- **Changed** for changes in existing functionality
- **Deprecated** for soon-to-be removed features
- **Removed** for now removed features
- **Fixed** for any bug fixes
- **Security** for vulnerability fixes

### Categories

- **Frontend** - SvelteKit application changes
- **Backend** - FastAPI application changes
- **Demo Processing** - Microservice changes
- **Infrastructure** - Docker, CI/CD, deployment changes
- **Documentation** - Documentation updates
- **Dependencies** - Dependency updates
